<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>飞控子工序生产看板</title>
  <script src="http://*************:50401/dv/d66227b32198acbeca55/js/chart.js"></script>
  <style>
    :root{
      --primary-color:#00f2fe;--secondary-color:#4facfe;--accent-color:#00ff9d;
      --dark-bg:#0f172a;--panel-bg:rgba(15,23,42,.7);--text-color:#e2e8f0;--text-muted:#94a3b8
    }
    *{margin:0;padding:0;box-sizing:border-box;font-family:Arial,system-ui,-apple-system,Segoe UI,Roboto,sans-serif}
    body{background:var(--dark-bg);color:var(--text-color);overflow:hidden;height:100vh;width:100vw;
      background-image:radial-gradient(circle at 10% 20%,rgba(0,242,254,.1) 0%,transparent 20%),
      radial-gradient(circle at 90% 80%,rgba(79,172,254,.1) 0%,transparent 20%)}
    .dashboard{width:100%;height:100%;padding:1.5vmin;display:grid;
      grid-template-columns:1.2fr .8fr 1fr;grid-template-rows:8vh 1fr 1fr;gap:1.5vmin;position:relative}
    .header{grid-column:1/4;background:linear-gradient(90deg,var(--primary-color),var(--secondary-color));
      color:var(--dark-bg);display:flex;align-items:center;justify-content:center;border-radius:1vmin;
      box-shadow:0 0 2vmin rgba(0,242,254,.3);position:relative;overflow:hidden;z-index:1}
    .header::before{content:"";position:absolute;top:-50%;left:-50%;width:200%;height:200%;
      background:linear-gradient(to bottom right,rgba(255,255,255,.3) 0%,rgba(255,255,255,0) 60%);
      transform:rotate(30deg);z-index:-1}
    .header h1{font-size:3.5vmin;font-weight:700;letter-spacing:.5vmin;text-transform:uppercase}
    .time-display{position:absolute;top:50%;right:3vmin;transform:translateY(-50%);
      background:rgba(15,23,42,.7);padding:1vmin 2vmin;border-radius:2vmin;font-size:2vmin;
      box-shadow:0 0 1vmin rgba(0,0,0,.3);border:1px solid rgba(79,172,254,.3);display:flex;align-items:center}
    .time-display::before{content:"";display:inline-block;width:1.5vmin;height:1.5vmin;background:var(--accent-color);
      border-radius:50%;margin-right:1vmin;box-shadow:0 0 1vmin var(--accent-color)}
    .panel{background:var(--panel-bg);border-radius:1.5vmin;padding:2vmin;box-shadow:0 0 1.5vmin rgba(0,0,0,.3);
      display:flex;flex-direction:column;border:1px solid rgba(79,172,254,.2);position:relative;overflow:hidden}
    .panel::after{content:"";position:absolute;top:0;left:0;right:0;height:.3vmin;
      background:linear-gradient(90deg,var(--primary-color),var(--secondary-color))}
    .panel-title{font-size:2.2vmin;font-weight:700;margin-bottom:1.2vmin;color:var(--primary-color);display:flex;align-items:center}
    .panel-title::before{content:"";display:inline-block;width:.8vmin;height:2vmin;background:var(--accent-color);
      margin-right:1vmin;border-radius:.4vmin}
    .data-table{width:100%;border-collapse:collapse;flex-grow:1;font-size:1.8vmin}
    .data-table th,.data-table td{border:1px solid rgba(79,172,254,.2);padding:1.2vmin;text-align:center}
    .data-table th{background:rgba(79,172,254,.1);font-weight:700;color:var(--primary-color)}
    .data-table tr:nth-child(even){background:rgba(15,23,42,.5)}
    .data-table tr:hover{background:rgba(79,172,254,.1)}
    .chart-container{position:relative;height:calc(100% - 4vmin);width:100%}
    .kpi-container{display:grid;grid-template-columns:1fr 1fr;grid-template-rows:1fr 1fr;gap:1.2vmin;margin-top:1.2vmin}
    .kpi-item{background:rgba(15,23,42,.7);border-radius:1vmin;padding:1.8vmin;display:flex;flex-direction:column;align-items:center;
      justify-content:center;border:1px solid rgba(79,172,254,.2);box-shadow:0 0 1vmin rgba(0,0,0,.2);position:relative}
    .kpi-item::before{content:"";position:absolute;bottom:0;left:0;right:0;height:.3vmin;background:linear-gradient(90deg,var(--primary-color),var(--secondary-color))}
    .kpi-label{font-size:1.7vmin;color:var(--text-muted);margin-bottom:.8vmin}
    .kpi-value{font-size:3.2vmin;font-weight:700;color:var(--primary-color)}
    .positive{color:var(--accent-color)} .negative{color:#ff6b6b}
    .glow-effect{position:absolute;width:100%;height:100%;top:0;left:0;background:radial-gradient(circle at center,rgba(0,242,254,.1) 0%,transparent 70%);pointer-events:none;z-index:-1}
    .watermark{position:absolute;left:2vmin;bottom:2vmin;font-size:1.8vmin;color:rgba(148,163,184,.6);z-index:10}
    /* 坏机原因排行 */
    .reason-container{margin-top:1vmin;display:flex;flex-direction:column;gap:.4vmin}
    .reason-row{display:grid;grid-template-columns:3vmin 1fr auto auto;gap:1vmin;align-items:center;padding:.6vmin .2vmin;border-bottom:1px solid rgba(79,172,254,.08)}
    .reason-rank{width:3vmin;height:3vmin;border-radius:50%;display:flex;align-items:center;justify-content:center;
      font-size:1.6vmin;font-weight:700;color:#ff6b6b;background:rgba(255,107,107,.25)}
    .reason-name{font-size:1.8vmin;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}
    .reason-count{font-size:1.8vmin;font-weight:700;color:#ff6b6b;background:rgba(15,23,42,.7);padding:.3vmin 1vmin;border-radius:2vmin;border:1px solid rgba(255,107,107,.35)}
    .reason-percent{font-size:1.6vmin;color:#94a3b8;text-align:right;width:6vmin}
  </style>
</head>
<body>
  <div class="dashboard">
    <div class="watermark">广东科贸吴菀毓，胡江飞制</div>

    <div class="header">
      <h1>飞控子工序生产看板</h1>
      <div class="time-display" id="current-time"></div>
      <div class="glow-effect"></div>
    </div>

    <div class="panel">
      <div class="panel-title">生产数据总览</div>
      <table class="data-table" id="summary-table">
        <thead>
        <tr>
          <th>产品编号</th>
          <th>总生产数量</th>
          <th>生产效率</th>
          <th>平均生产时间</th>
          <th>合格数</th>
          <th>错误数</th>
        </tr>
        </thead>
        <tbody></tbody>
      </table>
      <div class="glow-effect"></div>
    </div>

    <div class="panel">
      <div class="panel-title">7天生产数量趋势</div>
      <div class="chart-container"><canvas id="weeklyProductionChart"></canvas></div>
      <div class="glow-effect"></div>
    </div>

    <div class="panel">
      <div class="panel-title">每小时生产数据</div>
      <div class="chart-container"><canvas id="hourlyProductionChart"></canvas></div>
      <div class="glow-effect"></div>
    </div>

    <div class="panel">
      <div class="panel-title">质量分析</div>
      <div class="kpi-container" id="kpi-cards"></div>
      <div class="glow-effect"></div>
    </div>

    <div class="panel">
      <div class="panel-title">每小时生产效率</div>
      <div class="chart-container"><canvas id="efficiencyChart"></canvas></div>
      <div class="glow-effect"></div>
    </div>

    <div class="panel">
      <div class="panel-title">坏机原因排行</div>
      <div class="chart-container"><canvas id="errorReasonChart"></canvas></div>
      <div class="reason-container" id="reason-list"></div>
      <div class="glow-effect"></div>
    </div>
  </div>

<script>
(() => {
  'use strict';

  // ===== 全局 Chart 样式 =====
  Chart.defaults.color = '#e2e8f0';
  Chart.defaults.borderColor = 'rgba(79,172,254,0.2)';

  // ===== 统一数值标签插件（复用，避免多次注册）=====
  const ValueLabelsPlugin = {
    id: 'valueLabels',
    afterDatasetsDraw(chart, args, opts) {
      const cfg = Object.assign({ fontSize: 12, color: '#fff', offset: 4, formatter: v => v }, opts || {});
      const ctx = chart.ctx;
      ctx.save();
      ctx.font = `bold ${cfg.fontSize}px Arial`;
      ctx.fillStyle = cfg.color;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'bottom';

      chart.data.datasets.forEach((ds, i) => {
        const meta = chart.getDatasetMeta(i);
        if (!meta || meta.hidden) return;
        meta.data.forEach((el, idx) => {
          const val = ds.data[idx];
          if (val == null) return;
          const p = el.tooltipPosition ? el.tooltipPosition() : { x: el.x, y: el.y };
          const text = cfg.formatter(val, ds, idx);
          ctx.fillText(text, p.x, p.y - cfg.offset);
        });
      });
      ctx.restore();
    }
  };
  Chart.register(ValueLabelsPlugin);

  // ===== 工具：时间与随机 =====
  const $ = sel => document.querySelector(sel);
  let clockTimer = null;
  let dataRefreshTimer = null;

  function startClock() {
    const el = $('#current-time');
    const tick = () => {
      const now = new Date();
      const pad = n => String(n).padStart(2, '0');
      const s = `${now.getFullYear()}-${pad(now.getMonth()+1)}-${pad(now.getDate())} `
              + `${pad(now.getHours())}:${pad(now.getMinutes())}:${pad(now.getSeconds())}`;
      el.textContent = s;
    };
    tick();
    clockTimer = setInterval(tick, 1000);
  }

  function stopClock() {
    if (clockTimer) { clearInterval(clockTimer); clockTimer = null; }
  }

  function randInt(min, max) { return Math.floor(Math.random()*(max-min+1))+min; }
  function clamp(n, a, b){ return Math.max(a, Math.min(b, n)); }

  // ===== API数据获取 =====
  const API_BASE_URL = 'http://*************:5001/api/dashboard';
  const LINE_NUMBER = 1; // 默认生产线1

  async function fetchData(endpoint) {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`);
      if (!response.ok) {
        throw new Error(`API错误: ${response.status}`);
      }
      const data = await response.json();

      // 检查是否有错误
      if (data.error) {
        throw new Error(data.error);
      }

      return data;
    } catch (error) {
      console.error(`获取数据失败: ${error.message}`);
      return null;
    }
  }

  async function fetchAllData() {
    try {
      console.log('正在获取Dashboard API数据...');

      // 尝试获取所有数据的合并接口
      const allData = await fetchData(`/all/${LINE_NUMBER}`);

      if (allData) {
        console.log('成功获取合并数据:', allData);
        return processNewApiData(allData);
      }

      // 如果合并接口失败，尝试分别获取各个接口
      console.log('合并接口失败，尝试分别获取各个接口...');
      const [overview, trend, hourly, quality, efficiency, malfunction] = await Promise.all([
        fetchData(`/overview/${LINE_NUMBER}`),
        fetchData(`/trend/${LINE_NUMBER}`),
        fetchData(`/hourly/${LINE_NUMBER}`),
        fetchData(`/quality/${LINE_NUMBER}`),
        fetchData(`/efficiency/${LINE_NUMBER}`),
        fetchData(`/malfunction/${LINE_NUMBER}?days=7`)
      ]);

      // 只要有任何一个接口返回数据就处理
      console.log('分离接口结果:', { overview: !!overview, trend: !!trend, hourly: !!hourly, quality: !!quality, efficiency: !!efficiency, malfunction: !!malfunction });
      return processNewApiDataSeparate(overview, trend, hourly, quality, efficiency, malfunction);

    } catch (error) {
      console.error('获取数据失败:', error);
      throw error; // 不再返回模拟数据，直接抛出错误
    }
  }

  // 处理合并API数据（/all接口）
  function processNewApiData(data) {
    console.log('处理合并API数据:', data);

    if (!validateApiData(data, '合并API')) {
      throw new Error('合并API数据无效');
    }

    // 处理7天趋势数据
    const weeklyLabels = data.trend_dates ? data.trend_dates.map(date => {
      const d = new Date(date);
      return `${String(d.getMonth()+1).padStart(2,'0')}/${String(d.getDate()).padStart(2,'0')}`;
    }) : [];
    const weeklyData = data.trend_production_counts || [];

    // 处理每小时数据
    const hourlyLabels = data.hourly_hours || [];
    const hourlyProd = data.hourly_production_counts || [];
    // 暂时没有错误数据，使用0填充
    const hourlyErr = new Array(hourlyLabels.length).fill(0);

    // 处理效率数据
    const effLabels = data.efficiency_hours || hourlyLabels;
    const effData = data.efficiency_efficiency_rates || [];

    // 处理KPI数据 - 使用实际数据，如果没有则显示0
    const pass = data.quality_qualified_rate !== undefined ? data.quality_qualified_rate.toFixed(1) : "0.0";
    const ng = data.quality_defect_rate !== undefined ? data.quality_defect_rate.toFixed(1) : "0.0";
    const avgEff = data.efficiency_avg_efficiency !== undefined ? data.efficiency_avg_efficiency.toFixed(1) : "0.0";
    const avgTime = data.avg_cycle_time !== undefined ? data.avg_cycle_time.toFixed(1) : "0.0";

    // 生成总览表数据
    const summary = [{
      id: data.product_id || 'N/A',
      total: data.total_production || 0,
      eff: data.production_efficiency !== undefined ? data.production_efficiency.toFixed(1) + '%' : '0.0%',
      avg: avgTime + '分钟',
      ok: data.quality_qualified_products || 0,
      bad: data.quality_defective_products || 0
    }];

    // 处理故障排行数据
    const reasons = [];
    if (data.ranking_malfunction_types && data.ranking_malfunction_counts) {
      for (let i = 0; i < data.ranking_malfunction_types.length; i++) {
        reasons.push({
          name: data.ranking_malfunction_types[i],
          count: data.ranking_malfunction_counts[i],
          percent: data.ranking_malfunction_percentages ? data.ranking_malfunction_percentages[i] : 0
        });
      }
    }

    console.log('合并数据处理完成:', {
      weeklyLabels: weeklyLabels.length,
      weeklyData: weeklyData.length,
      hourlyLabels: hourlyLabels.length,
      hourlyProd: hourlyProd.length,
      effData: effData.length,
      summary: summary[0],
      reasons: reasons.length
    });

    return {
      weekly: { labels: weeklyLabels, data: weeklyData },
      hourly: { labels: hourlyLabels, prod: hourlyProd, err: hourlyErr },
      eff: { labels: effLabels, data: effData },
      kpi: { pass, ng, avgEff, avgTime: avgTime + '分钟' },
      summary,
      reasons
    };
  }

  // 处理分离API数据
  function processNewApiDataSeparate(overview, trend, hourly, quality, efficiency, malfunction) {
    console.log('处理分离API数据:', { overview: !!overview, trend: !!trend, hourly: !!hourly, quality: !!quality, efficiency: !!efficiency, malfunction: !!malfunction });

    // 处理7天趋势 - 如果没有数据则显示空图表
    const weeklyLabels = trend && trend.dates ? trend.dates.map(date => {
      const d = new Date(date);
      return `${String(d.getMonth()+1).padStart(2,'0')}/${String(d.getDate()).padStart(2,'0')}`;
    }) : [];
    const weeklyData = trend ? trend.production_counts || [] : [];

    // 处理每小时数据 - 如果没有数据则显示空图表
    const hourlyLabels = hourly ? hourly.hours || [] : [];
    const hourlyProd = hourly ? hourly.production_counts || [] : [];
    const hourlyErr = new Array(hourlyLabels.length).fill(0); // 暂无错误数据

    // 处理效率数据 - 如果没有数据则显示空图表
    const effLabels = efficiency ? efficiency.hours || [] : hourlyLabels;
    const effData = efficiency ? efficiency.efficiency_rates || [] : [];

    // 处理KPI - 使用实际数据，如果没有则显示0或N/A
    const pass = quality && quality.qualified_rate !== undefined ? quality.qualified_rate.toFixed(1) : "0.0";
    const ng = quality && quality.defect_rate !== undefined ? quality.defect_rate.toFixed(1) : "0.0";
    const avgEff = efficiency && efficiency.avg_efficiency !== undefined ? efficiency.avg_efficiency.toFixed(1) : "0.0";
    const avgTime = overview && overview.avg_cycle_time !== undefined ? overview.avg_cycle_time.toFixed(1) : "0.0";

    // 总览表 - 使用实际数据
    const summary = [{
      id: overview && overview.product_id ? overview.product_id : 'N/A',
      total: overview && overview.total_production !== undefined ? overview.total_production : 0,
      eff: overview && overview.production_efficiency !== undefined ? overview.production_efficiency.toFixed(1) + '%' : '0.0%',
      avg: avgTime + '分钟',
      ok: quality && quality.qualified_products !== undefined ? quality.qualified_products : 0,
      bad: quality && quality.defective_products !== undefined ? quality.defective_products : 0
    }];

    // 故障排行 - 使用实际数据
    const reasons = [];
    if (malfunction && malfunction.malfunction_types && malfunction.malfunction_counts) {
      for (let i = 0; i < malfunction.malfunction_types.length; i++) {
        reasons.push({
          name: malfunction.malfunction_types[i],
          count: malfunction.malfunction_counts[i],
          percent: malfunction.malfunction_percentages ? malfunction.malfunction_percentages[i] : 0
        });
      }
    }

    console.log('处理后的数据:', {
      weeklyLabels: weeklyLabels.length,
      weeklyData: weeklyData.length,
      hourlyLabels: hourlyLabels.length,
      hourlyProd: hourlyProd.length,
      effData: effData.length,
      summary: summary[0],
      reasons: reasons.length
    });

    return {
      weekly: { labels: weeklyLabels, data: weeklyData },
      hourly: { labels: hourlyLabels, prod: hourlyProd, err: hourlyErr },
      eff: { labels: effLabels, data: effData },
      kpi: { pass, ng, avgEff, avgTime: avgTime + '分钟' },
      summary,
      reasons
    };
  }

  // ===== 数据验证和处理工具 =====
  function validateApiData(data, type) {
    if (!data) {
      console.warn(`${type} 数据为空`);
      return false;
    }

    if (data.error) {
      console.error(`${type} API错误:`, data.error);
      return false;
    }

    console.log(`${type} 数据验证通过:`, data);
    return true;
  }

  function createEmptyData() {
    // 创建空数据结构，用于显示空图表而不是错误
    return {
      weekly: { labels: [], data: [] },
      hourly: { labels: [], prod: [], err: [] },
      eff: { labels: [], data: [] },
      kpi: { pass: "0.0", ng: "0.0", avgEff: "0.0", avgTime: "0.0分钟" },
      summary: [{ id: 'N/A', total: 0, eff: '0.0%', avg: '0.0分钟', ok: 0, bad: 0 }],
      reasons: []
    };
  }

  // ===== App：统一创建/更新/释放 =====
  const App = {
    charts: { weekly:null, hourly:null, eff:null, reason:null },
    listeners: [],
    hasLoadedSuccessfully: false, // 跟踪是否成功加载过数据
    init() {
      startClock();
      this.buildCharts();
      
      // 初始加载数据
      this.loadRealData();

      // 设置定时刷新（每30秒，更频繁地获取实时数据）
      dataRefreshTimer = setInterval(() => this.loadRealData(), 30000);
      
      // 管理事件监听（便于释放）
      const onHide = () => { if (document.hidden) stopClock(); else startClock(); };
      const onUnload = () => this.dispose();
      document.addEventListener('visibilitychange', onHide);
      window.addEventListener('beforeunload', onUnload);
      this.listeners.push(['visibilitychange', onHide, document], ['beforeunload', onUnload, window]);
    },
    
    async loadRealData() {
      try {
        console.log('开始加载数据...', new Date().toLocaleTimeString());
        const data = await fetchAllData();

        // 直接使用API数据，不再检查是否为null
        this.fillSummary(data.summary);
        this.fillKpis(data.kpi);
        this.updateWeekly(data.weekly);
        this.updateHourly(data.hourly);
        this.updateEff(data.eff);
        this.updateReasons(data.reasons);

        // 更新状态指示器
        this.updateConnectionStatus(true);
        this.hasLoadedSuccessfully = true; // 标记成功加载
        console.log('✅ 数据已更新:', new Date().toLocaleTimeString());

      } catch (error) {
        console.error('❌ 加载数据失败:', error);
        this.updateConnectionStatus(false);
        this.showErrorMessage('API连接失败，请检查服务器状态');

        // 如果是初始加载失败，停止定时刷新
        if (!this.hasLoadedSuccessfully) {
          console.warn('初始数据加载失败，停止定时刷新');
          if (dataRefreshTimer) {
            clearInterval(dataRefreshTimer);
            dataRefreshTimer = null;
          }
        }
      }
    },

    showErrorMessage(message) {
      // 在页面上显示错误信息而不是使用模拟数据
      const panels = document.querySelectorAll('.panel');
      panels.forEach(panel => {
        const content = panel.querySelector('.chart-container, .data-table, .kpi-container, .reason-container');
        if (content) {
          content.innerHTML = `
            <div style="
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              height: 100%;
              color: #ff6b6b;
              text-align: center;
              padding: 2vmin;
            ">
              <div style="font-size: 3vmin; margin-bottom: 1vmin;">⚠️</div>
              <div style="font-size: 1.8vmin; margin-bottom: 1vmin;">${message}</div>
              <div style="font-size: 1.4vmin; color: #94a3b8;">
                请检查API服务器是否正常运行<br>
                服务器地址: ${API_BASE_URL}
              </div>
            </div>
          `;
        }
      });
    },

    updateConnectionStatus(connected) {
      // 在时间显示器旁边添加连接状态指示
      const timeDisplay = document.getElementById('current-time');
      const existing = timeDisplay.querySelector('.connection-status');
      if (existing) existing.remove();

      const status = document.createElement('span');
      status.className = 'connection-status';
      status.style.cssText = `
        margin-left: 1vmin;
        padding: 0.3vmin 0.8vmin;
        border-radius: 1vmin;
        font-size: 1.4vmin;
        font-weight: bold;
        ${connected
          ? 'background: rgba(0,255,157,0.2); color: #00ff9d; border: 1px solid rgba(0,255,157,0.3);'
          : 'background: rgba(255,107,107,0.2); color: #ff6b6b; border: 1px solid rgba(255,107,107,0.3);'
        }
      `;
      status.textContent = connected ? '实时数据' : '连接失败';
      status.title = connected
        ? `API连接正常 - ${API_BASE_URL}`
        : `API连接失败 - ${API_BASE_URL}`;
      timeDisplay.appendChild(status);
    },


    fillSummary(rows){
      const tbody = document.querySelector('#summary-table tbody');
      tbody.innerHTML = '';
      const frag = document.createDocumentFragment();
      rows.forEach(r=>{
        const tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${r.id}</td>
          <td class="data-highlight">${r.total.toLocaleString()}</td>
          <td class="positive">${r.eff}</td>
          <td>${r.avg}</td>
          <td class="positive">${Number(r.ok).toLocaleString()}</td>
          <td class="negative">${Number(r.bad).toLocaleString()}</td>`;
        frag.appendChild(tr);
      });
      tbody.appendChild(frag);
    },
    fillKpis(k){
      const el = document.getElementById('kpi-cards');
      el.innerHTML = `
        <div class="kpi-item"><div class="kpi-label">今日PASS率</div><div class="kpi-value positive">${k.pass}%</div></div>
        <div class="kpi-item"><div class="kpi-label">今日NG率</div><div class="kpi-value negative">${k.ng}%</div></div>
        <div class="kpi-item"><div class="kpi-label">平均生产效率</div><div class="kpi-value positive">${k.avgEff}%</div></div>
        <div class="kpi-item"><div class="kpi-label">平均生产时间</div><div class="kpi-value">${k.avgTime}</div></div>`;
    },
    buildCharts(){
      // 7天
      this.charts.weekly = new Chart(document.getElementById('weeklyProductionChart').getContext('2d'), {
        type:'bar',
        data:{ labels:[], datasets:[{ label:'生产数量', data:[],
          backgroundColor:'rgba(0,242,254,0.7)', borderColor:'rgba(0,242,254,1)', borderWidth:1, borderRadius:4 }]},
        options:{
          responsive:true, maintainAspectRatio:false, animation:false,
          plugins:{ legend:{ display:true, position:'top' }, valueLabels:{ fontSize:12, formatter:v=>v } },
          scales:{ y:{ beginAtZero:true, grid:{ color:'rgba(79,172,254,0.1)' }, title:{ display:true, text:'生产数量' }},
                   x:{ grid:{ color:'rgba(79,172,254,0.1)' }, reverse: true } }
        }
      });

      // 每小时生产/错误
      this.charts.hourly = new Chart(document.getElementById('hourlyProductionChart').getContext('2d'), {
        type:'line',
        data:{ labels:[], datasets:[
          { label:'生产数量', data:[], fill:true, tension:.3, borderWidth:2,
            backgroundColor:'rgba(0,242,254,0.2)', borderColor:'rgba(0,242,254,1)', yAxisID:'y' },
          { label:'错误数', data:[], fill:false, tension:.3, borderWidth:2,
            backgroundColor:'rgba(255,107,107,0.2)', borderColor:'rgba(255,107,107,1)', yAxisID:'y1' }
        ]},
        options:{
          responsive:true, maintainAspectRatio:false, animation:false,
          plugins:{ legend:{ display:true, position:'top' }, valueLabels:{ fontSize:11, formatter:v=>v } },
          scales:{ y:{ position:'left', grid:{ color:'rgba(79,172,254,0.1)'} , title:{display:true,text:'生产数量'} },
                   y1:{ position:'right', grid:{ drawOnChartArea:false }, title:{display:true,text:'错误数'} },
                   x:{ grid:{ color:'rgba(79,172,254,0.1)'}, reverse: true } }
        }
      });

      // 每小时效率
      this.charts.eff = new Chart(document.getElementById('efficiencyChart').getContext('2d'), {
        type:'line',
        data:{ labels:[], datasets:[
          { label:'生产效率 (%)', data:[], fill:true, tension:.3, borderWidth:2,
            backgroundColor:'rgba(0,255,157,0.2)', borderColor:'rgba(0,255,157,1)' }
        ]},
        options:{
          responsive:true, maintainAspectRatio:false, animation:false,
          plugins:{ legend:{ display:true, position:'top' }, valueLabels:{ fontSize:11, formatter:v=>`${v}%` } },
          scales:{ y:{ min:85, max:100, grid:{ color:'rgba(79,172,254,0.1)'}, title:{display:true,text:'生产效率 (%)'}},
                   x:{ grid:{ color:'rgba(79,172,254,0.1)'}, reverse: true } }
        }
      });

      // 坏机原因（横向）
      this.charts.reason = new Chart(document.getElementById('errorReasonChart').getContext('2d'), {
        type:'bar',
        data:{ labels:[], datasets:[{ label:'坏机次数', data:[],
          backgroundColor:[
            'rgba(255,99,132,0.7)','rgba(255,159,64,0.7)','rgba(255,205,86,0.7)','rgba(75,192,192,0.7)','rgba(54,162,235,0.7)'
          ], borderColor:[
            'rgba(255,99,132,1)','rgba(255,159,64,1)','rgba(255,205,86,1)','rgba(75,192,192,1)','rgba(54,162,235,1)'
          ], borderWidth:1, borderRadius:4 }]},
        options:{
          indexAxis:'y', responsive:true, maintainAspectRatio:false, animation:false,
          plugins:{ legend:{ display:false }, valueLabels:{ fontSize:12, formatter:(v,ds,i)=>{
            const total = ds.data.reduce((a,b)=>a+b,0)||1; const p = Math.round(v*100/total);
            return `${v} (${p}%)`;
          }}},
          scales:{ x:{ beginAtZero:true, grid:{ color:'rgba(79,172,254,0.1)'}, title:{display:true,text:'坏机次数'} },
                   y:{ grid:{ color:'rgba(79,172,254,0.1)'} } }
        }
      });
    },
    updateWeekly({labels, data}){
      const c = this.charts.weekly;
      c.data.labels = labels.slice();
      c.data.datasets[0].data = data.slice();
      c.update('none');
    },
    updateHourly({labels, prod, err}){
      const c = this.charts.hourly;
      c.data.labels = labels.slice();
      c.data.datasets[0].data = prod.slice();
      c.data.datasets[1].data = err.slice();
      c.update('none');
    },
    updateEff({labels, data}){
      const c = this.charts.eff;
      c.data.labels = labels.slice();
      c.data.datasets[0].data = data.slice();
      c.update('none');
    },
    updateReasons(list){
      const c = this.charts.reason;
      c.data.labels = list.map(r=>r.name);
      c.data.datasets[0].data = list.map(r=>r.count);
      c.update('none');

      // 更新文本列表
      const box = document.getElementById('reason-list');
      box.innerHTML = '';

      if (list && list.length > 0) {
        const frag = document.createDocumentFragment();
        list.forEach((reason, index) => {
          const div = document.createElement('div');
          div.className = 'reason-row';
          div.innerHTML = `
            <div class="reason-rank">${index + 1}</div>
            <div class="reason-name">${reason.name}</div>
            <div class="reason-count">${reason.count}</div>
            <div class="reason-percent">${reason.percent}%</div>
          `;
          frag.appendChild(div);
        });
        box.appendChild(frag);
      } else {
        // 如果没有故障数据，显示提示
        box.innerHTML = '<div style="text-align:center;color:#94a3b8;padding:2vmin;">暂无故障数据</div>';
      }
    },
    dispose(){
      // 清理时钟
      stopClock();

      // 清理数据刷新定时器
      if (dataRefreshTimer) {
        clearInterval(dataRefreshTimer);
        dataRefreshTimer = null;
      }

      // 移除监听
      this.listeners.forEach(([evt, fn, target]) => (target||document).removeEventListener(evt, fn));
      this.listeners = [];

      // 销毁图表释放内存
      Object.keys(this.charts).forEach(k=>{
        const inst = this.charts[k];
        if (inst) { try { inst.clear(); inst.destroy(); } catch(e){} this.charts[k]=null; }
      });

      // 重置状态
      this.hasLoadedSuccessfully = false;
    }
  };

  // 暴露给外部（便于你后续 setData / 手动释放）
  window.DashboardApp = App;

  // 启动
  App.init();

})();
</script>
</body>
</html>